const express = require('express');
const multer = require('multer');
// Removed unused 'path' import
const cors = require('cors');
const nodemailer = require("nodemailer");
const { MongoClient } = require("mongodb");
require('dotenv/config');
const { getContestImages } = require('./contestData');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const hpp = require('hpp');
// crypto module is not used directly in this file.
const { connectToDb } = require('./dbConnect');
const { ObjectId } = require('mongodb');
const jwt = require('jsonwebtoken');
const cookieParser = require('cookie-parser');
const csrf = require('csurf');

// Secret key for JWT
const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  // Throwing an error is often preferred over process.exit in modules
  // as it allows potential higher-level handling or clearer stack traces.
  // The application will likely terminate due to the uncaught exception anyway.
  throw new Error('FATAL ERROR: JWT_SECRET environment variable is not defined. Application cannot start.');
}


// Middleware to verify admin JWT token
const verifyAdminToken = (req, res, next) => {
  // Debug logging
  console.log('Cookies received:', req.cookies);
  console.log('Headers:', req.headers.cookie);

  // Ensure cookies object exists before trying to access adminToken
  const token = req.cookies && req.cookies.adminToken;

  if (!token) {
    console.log('No adminToken cookie found');
    return res.status(401).json({ success: false, message: 'No authentication token provided' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.admin = decoded;
    console.log('Token verified successfully for:', decoded.username);
    next();
  } catch (err) {
    // Log specific error details for server-side debugging
    console.error(`Token verification failed: ${err.name} - ${err.message}`);
    // Return a generic error message to the client for security
    return res.status(401).json({ success: false, message: 'Invalid or expired token' });
  }
};

const PORT = process.env.PORT || 3001;

const app = express();

app.get('/', (req, res) => {
  res.send('Server is running');
});

// Security middleware
// Set security HTTP headers
app.use(helmet());

// Enable CORS for cross-origin requests with specific origin
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? ['https://pickle-boy.com', 'https://www.pickle-boy.com', 'http://localhost:3000']
    : ['http://localhost:3000', 'http://127.0.0.1:3000'], // Add 127.0.0.1 as well
  optionsSuccessStatus: 200,
  credentials: true // Allow credentials (cookies)
};
app.use(cors(corsOptions));

// Serve static files from the 'server' directory under the '/images' path
// Add explicit CORS and CORP headers for static files
app.use('/images', (req, res, next) => {
  // Set permissive headers for development, restrictive for production
  if (process.env.NODE_ENV === 'production') {
    const allowedOrigins = ['https://pickle-boy.com', 'https://www.pickle-boy.com', 'http://localhost:3000'];
    const origin = req.headers.origin;
    if (allowedOrigins.includes(origin)) {
      res.header('Access-Control-Allow-Origin', origin);
    }
  } else {
    // Development: Allow all origins
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Add Cross-Origin Resource Policy header - this is crucial for the error you're seeing
  res.header('Cross-Origin-Resource-Policy', 'cross-origin');

  // Add Cross-Origin Embedder Policy for additional compatibility
  res.header('Cross-Origin-Embedder-Policy', 'unsafe-none');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }

  next();
}, express.static('server'));

// Cookie parser middleware - MUST be before routes that use cookies
app.use(cookieParser());

// CSRF Protection Middleware - only for admin routes that modify data
const csrfProtection = csrf({ cookie: true });
// Note: CSRF protection will be applied selectively to routes that need it

// Limit requests from same API
const limiter = rateLimit({
  max: 100, // limit each IP to 100 requests per windowMs
  windowMs: 60 * 60 * 1000, // 1 hour
  message: 'Too many requests from this IP, please try again in an hour!'
});
app.use('/api', limiter);

// Body parser, reading data from body into req.body
app.use(express.json({ limit: '10kb' })); // limit body size to 10kb
app.use(express.urlencoded({ extended: true, limit: '10kb' }));

// Data sanitization against NoSQL query injection
app.use(mongoSanitize());

// Prevent parameter pollution
app.use(hpp());

// Set security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  next();
});

// News API endpoints
// GET all news items
app.get('/api/news', async (req, res) => {
  try {
    const client = await connectToDb();
    const news = await client.db("PickleBoy_DB").collection("News")
      .find({})
      .sort({ date: -1 }) // Sort by date descending (newest first)
      .toArray();

    res.json(news);
  } catch (err) {
    console.error('Error fetching news items:', err);
    res.status(500).json({ success: false, message: 'Failed to fetch news items' });
  }
});

// POST a new news item
app.post('/api/news', csrfProtection, verifyAdminToken, async (req, res) => {
  try {
    const { title, content, date } = req.body;

    if (!title || !content) {
      return res.status(400).json({ success: false, message: 'Title and content are required' });
    }

    const newsItem = {
      title,
      content,
      date: date || new Date().toISOString()
    };

    const client = await connectToDb();
    const result = await client.db("PickleBoy_DB").collection("News").insertOne(newsItem);

    // Return the created item with its new _id
    res.status(201).json({ ...newsItem, _id: result.insertedId });
  } catch (err) {
    console.error('Error creating news item:', err);
    res.status(500).json({ success: false, message: 'Failed to create news item' });
  }
});

// PUT (update) a news item
app.put('/api/news/:id', csrfProtection, verifyAdminToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content } = req.body;

    if (!title && !content) {
      return res.status(400).json({ success: false, message: 'At least one field (title or content) is required for update' });
    }

    const client = await connectToDb();

    // Create update object with only the fields that are provided
    const updateData = {};
    if (title) updateData.title = title;
    if (content) updateData.content = content;

    const result = await client.db("PickleBoy_DB").collection("News").updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({ success: false, message: 'News item not found' });
    }

    // Fetch and return the updated item
    const updatedItem = await client.db("PickleBoy_DB").collection("News").findOne({ _id: new ObjectId(id) });
    res.json(updatedItem);
  } catch (err) {
    console.error('Error updating news item:', err);
    res.status(500).json({ success: false, message: 'Failed to update news item' });
  }
});

// DELETE a news item
app.delete('/api/news/:id', csrfProtection, verifyAdminToken, async (req, res) => {
  try {
    const { id } = req.params;

    const client = await connectToDb();
    const result = await client.db("PickleBoy_DB").collection("News").deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return res.status(404).json({ success: false, message: 'News item not found' });
    }

    res.json({ success: true, message: 'News item deleted successfully' });
  } catch (err) {
    console.error('Error deleting news item:', err);
    res.status(500).json({ success: false, message: 'Failed to delete news item' });
  }
});

// Determine email recipient based on environment
const emailSendTo = process.env.NODE_ENV === 'production'
  ? process.env.PROD_EMAIL_RECIPIENT || '<EMAIL>' // Fallback for production
  : process.env.TEST_EMAIL_RECIPIENT || '<EMAIL>'; // Fallback for development/testing

const pickleBoyEmail = process.env.SENDER_EMAIL || '<EMAIL>'; // Use env var for sender email too

// CORS is already enabled with specific options above

// Set up storage engine for multer (memory storage for database saving)
const storage = multer.memoryStorage(); // Store files in memory for database processing
const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // limit file size to 5MB
  fileFilter: (req, file, cb) => {
    // Allow only specific file types
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4'];
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, GIF images and MP4 videos are allowed.'), false);
    }
  }
});

// Handle multer errors
const handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 5MB.'
      });
    }
  }
  next(err);
};

app.use(handleMulterError);

// Static file serving moved to after CORS configuration above

// Endpoint to serve files from database
app.get('/images/:filename', async (req, res) => {
  let client;
  try {
    const filename = req.params.filename;

    client = await connectToDb();
    const file = await client.db("PickleBoy_DB").collection("Files").findOne({ filename: filename });

    if (!file) {
      return res.status(404).json({ success: false, message: 'File not found' });
    }

    // Convert base64 back to buffer
    const fileBuffer = Buffer.from(file.data, 'base64');

    // Set appropriate headers
    res.set({
      'Content-Type': file.mimetype,
      'Content-Length': fileBuffer.length,
      'Cache-Control': 'public, max-age=31536000' // Cache for 1 year
    });

    // Send the file
    res.send(fileBuffer);
  } catch (error) {
    console.error('Error serving file:', error);
    res.status(500).json({ success: false, message: 'Error serving file' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Input validation helper functions
const validateEmail = (email) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

const sanitizeInput = (input) => {
  if (typeof input !== 'string') return '';
  return input
    .trim()
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/&/g, '&amp;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

// FAQ data structure that organizes questions by episode
const createFAQArray = () => {
  return {
    episodeOne: [],
    episodeTwo: [],
    episodeThree: [],
    episodeFour: [],
    episodeFive: [],
    episodeSix: [],
    general: []
  };
};

// Function to update an existing FAQ question
async function updateFAQQuestion(questionId, updatedData) {
  let client;
  try {
    // Validate input
    if (!questionId || !updatedData || typeof updatedData !== 'object') {
      throw new Error('Invalid input: questionId and updatedData object are required.');
    }
    if (!ObjectId.isValid(questionId)) {
      throw new Error(`Invalid questionId format: ${questionId}`);
    }

    client = await connectToDb();

    // Only update allowed fields and sanitize them
    const allowedUpdates = {};
    if (updatedData.hasOwnProperty('question') && updatedData.question) allowedUpdates.question = sanitizeInput(updatedData.question);
    if (updatedData.hasOwnProperty('answer') && updatedData.answer) allowedUpdates.answer = sanitizeInput(updatedData.answer);
    if (updatedData.hasOwnProperty('name') && updatedData.name) allowedUpdates.name = sanitizeInput(updatedData.name);
    // Assuming age might be a string or number, sanitize if string
    if (updatedData.hasOwnProperty('age') && updatedData.age) allowedUpdates.age = typeof updatedData.age === 'string' ? sanitizeInput(updatedData.age) : updatedData.age;
    if (updatedData.hasOwnProperty('displayName') && updatedData.displayName) allowedUpdates.displayName = sanitizeInput(updatedData.displayName);
    if (updatedData.hasOwnProperty('episode') && updatedData.episode) {
      const sanitizedEpisode = sanitizeInput(updatedData.episode);
      allowedUpdates.episode = sanitizedEpisode;
      allowedUpdates.category = mapEpisodeToCategory(sanitizedEpisode);
    }
    // Add other fields as needed, ensuring sanitization

    // Add updatedAt timestamp
    allowedUpdates.updatedAt = new Date();

    // Ensure there's something to update besides the timestamp
    if (Object.keys(allowedUpdates).length <= 1) {
      throw new Error('No valid fields provided for update.');
    }

    const result = await client.db("PickleBoy_DB").collection("FAQs").updateOne(
      { _id: new ObjectId(questionId) },
      { $set: allowedUpdates }
    );

    // Check if a document was found
    if (result.matchedCount === 0) {
      throw new Error(`FAQ question with ID ${questionId} not found.`);
    }
    if (result.modifiedCount === 0) {
      console.log(`FAQ question with ID ${questionId} found but not modified (data might be the same).`);
    } else {
      console.log(`FAQ question updated successfully: ID ${questionId}`);
    }

    return result;
  } catch (err) {
    // Log the specific error message
    console.error(`Error updating FAQ question with ID ${questionId || 'unknown'}: ${err.message}`);
    // Re-throw the error to be handled by the caller or global error handler
    throw err;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Function to get all FAQ questions organized by episode
async function getFAQsByEpisode() {
  let client;
  try {
    client = await connectToDb();

    // Get all approved FAQ items
    const faqs = await client.db("PickleBoy_DB").collection("FAQs")
      .find({ approved: true })
      .sort({ createdAt: -1 })
      .toArray();

    // Organize by episode
    const organizedFAQs = createFAQArray();

    faqs.forEach(faq => {
      // Determine which episode this item belongs to
      let episodeKey = faq.episode || 'general';

      if (!faq.episode) {
        // If no episode is specified but has category, map category to episode
        if (faq.category === 'season1') {
          // Simple distribution for season 1 episodes
          const season1Episodes = ['episodeOne', 'episodeTwo', 'episodeThree'];
          const index = Math.floor(Math.random() * season1Episodes.length);
          episodeKey = season1Episodes[index];
        } else if (faq.category === 'season2') {
          // Simple distribution for season 2 episodes
          const season2Episodes = ['episodeFour', 'episodeFive', 'episodeSix'];
          const index = Math.floor(Math.random() * season2Episodes.length);
          episodeKey = season2Episodes[index];
        }
      }

      // Make sure the episode key exists in our structure
      if (!organizedFAQs[episodeKey]) {
        organizedFAQs[episodeKey] = [];
      }

      // Add the FAQ to the appropriate episode array
      organizedFAQs[episodeKey].push({
        id: faq._id,
        question: faq.question,
        answer: faq.answer,
        name: faq.name,
        age: faq.age,
        displayName: faq.displayName,
        type: faq.type
      });
    });

    return organizedFAQs;
  } catch (err) {
    console.error('Error fetching FAQs by episode:', err);
    // Return empty structure if there's an error
    return createFAQArray();
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Helper function to map episode keys to categories
function mapEpisodeToCategory(episodeKey) {
  if (['episodeOne', 'episodeTwo', 'episodeThree'].includes(episodeKey)) {
    return 'season1';
  } else if (['episodeFour', 'episodeFive', 'episodeSix'].includes(episodeKey)) {
    return 'season2';
  } else {
    return 'general';
  }
}

// Secure database operations

async function voteDB(voteObject) {
  let client;
  try {
    // Sanitize data before storing
    const sanitizedData = {};
    for (const [key, value] of Object.entries(voteObject)) {
      if (typeof value === 'string') {
        sanitizedData[key] = sanitizeInput(value);
      } else {
        sanitizedData[key] = value;
      }
    }

    // Add a unique identifier to the sanitized data
    sanitizedData._id = new ObjectId();
    // Add timestamp to track when vote was cast
    sanitizedData.timestamp = new Date();

    client = await connectToDb();
    const result = await client.db("PickleBoy_DB").collection("Votes").insertOne(sanitizedData);
    console.log("Vote added to database", result);
    return result;
  } catch (err) {
    console.error('Error with vote insertion:', err);
    throw err;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Get all votes for admin view
async function getPurimVotes() {
  let client;
  try {
    client = await connectToDb();
    const votes = await client.db("PickleBoy_DB").collection("Votes").find({}).toArray();
    return votes;
  } catch (err) {
    console.error('Error retrieving votes:', err);
    throw err;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Get tallied vote results
async function getTalliedPurimVotes() {
  let client;
  try {
    client = await connectToDb();

    // Get all costumes data to match with votes
    const costumeData = await client.db("PickleBoy_DB").collection("Purim2025Pictures").findOne({});
    const costumes = costumeData?.contestPurim2025Pictures?.costumes2025 || [];

    // Get all votes
    const votes = await client.db("PickleBoy_DB").collection("Votes").find({}).toArray();

    // Tally votes by counting votes for each costume ID
    const voteTally = {};
    votes.forEach(vote => {
      Object.entries(vote).forEach(([key, value]) => {
        if (key !== "_id" && key !== "timestamp" && value === 1) {
          voteTally[key] = (voteTally[key] || 0) + 1;
        }
      });
    });

    // Create results with costume details
    const results = costumes.map(costume => {
      return {
        id: costume.id,
        title: costume.title,
        votes: voteTally[costume.id] || 0,
        image: costume.images[0]
      };
    });

    // Sort by vote count in descending order
    results.sort((a, b) => b.votes - a.votes);

    return {
      totalVotes: votes.length,
      results: results
    };
  } catch (err) {
    console.error('Error retrieving tallied votes:', err);
    throw err;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Refactored function to insert FAQ data securely
async function insertFAQ(faqObject) {
  let client;
  try {
    // Sanitize input data
    const sanitizedData = {};
    for (const [key, value] of Object.entries(faqObject)) {
      if (typeof value === 'string') {
        sanitizedData[key] = sanitizeInput(value);
      } else {
        // Keep non-string values as is (e.g., boolean 'approved', 'displayName' might be boolean/string)
        sanitizedData[key] = value;
      }
    }

    // Add createdAt timestamp and ensure approved status is set correctly
    sanitizedData.createdAt = new Date();
    // Explicitly set approved to false unless provided otherwise (and validated)
    sanitizedData.approved = faqObject.approved === true; // Default to false if not provided or invalid

    client = await connectToDb(); // Use the helper function for connection
    const result = await client.db("PickleBoy_DB").collection("FAQs").insertOne(sanitizedData);
    console.log(`FAQ data inserted successfully with ID: ${result.insertedId}`);
    return result; // Return the result for potential use by the caller
  } catch (err) {
    // Log the specific error
    console.error(`Error inserting FAQ data: ${err.message}`, err);
    // Re-throw the error to be handled by the calling route handler
    throw err;
  } finally {
    // Ensure the client connection is closed if it was opened
    if (client) {
      await client.close();
    }
  }
}

// Refactored function to insert signup data securely
async function insertSignup(signupObject) {
  let client;
  try {
    // Sanitize input data - assuming signupObject contains 'username', 'email', 'password' etc.
    // Password should ideally be hashed *before* calling this function.
    const sanitizedData = {};
    for (const [key, value] of Object.entries(signupObject)) {
      // Basic sanitization for string values. Add more specific validation as needed.
      // Avoid storing raw passwords; ensure they are hashed elsewhere.
      if (key !== 'password' && typeof value === 'string') {
        sanitizedData[key] = sanitizeInput(value);
      } else {
        sanitizedData[key] = value; // Keep non-string values (like hashed password) as is
      }
    }
    // Add createdAt timestamp
    sanitizedData.createdAt = new Date();

    client = await connectToDb(); // Use the helper function for connection
    const result = await client.db("PickleBoy_DB").collection("Users").insertOne(sanitizedData);
    console.log(`User signup data inserted successfully with ID: ${result.insertedId}`);
    return result; // Return the result for potential use by the caller
  } catch (err) {
    // Log the specific error
    console.error(`Error inserting signup data: ${err.message}`, err);
    // Re-throw the error to be handled by the calling route handler
    throw err;
  } finally {
    // Ensure the client connection is closed if it was opened
    if (client) {
      await client.close();
    }
  }
}

async function insertSubscribe(subscribeObject) {
  const uri = process.env.DB_URI

  const client = new MongoClient(uri)
  try {
    await client.connect()
    const result = client.db("PickleBoy_DB").collection("SubscriptionForm").insertOne(subscribeObject)
      .then(() => console.log("DB Connected", result))
  }
  catch (err) {
    console.log('error with insert')
    console.log(err)
  }
}

async function insertSubmission(submissionObject) {
  const uri = process.env.DB_URI

  const client = new MongoClient(uri)
  try {
    await client.connect()
    const result = client.db("PickleBoy_DB").collection("SubmissionForm").insertOne(submissionObject)
      .then(() => console.log("DB Connected", result))
  }
  catch (err) {
    console.log('error with insert')
    console.log(err)
  }
}


//app.use(cors());
app.use(express.json());
//app.use(express.urlencoded({ extended: true }))

app.get("/message", (req, res) => {
  res.json({ message: "Hello from server!" });
});



const submittedArtwork = {
  listeningToPickleBoy: [
    // New category for "Listening to PickleBoy" submissions
    // Will be populated with audio submissions
  ],
  drawings: [
    {
      id: 7,
      title: "Bomb",
      desc: "",
      name: "",
      images: ["bombCropped.jpg", "bomb_cropped.jpg"],
      rotate: true
    },

    {
      id: 9,
      title: "Book Cover",
      desc: "",
      name: "",
      images: ["cover.PNG"],
      rotate: false
    },
    {
      id: 10,
      title: "Pickle Boy",
      desc: "",
      name: "",
      images: ["blackWhiteCoverCropped.jpg"],
      rotate: false
    },
    {
      id: 11,
      title: "Jail",
      desc: "",
      name: "",
      images: ["jail.PNG"],
      rotate: true
    },
    {
      id: 12,
      title: "Moishe's Basement",
      desc: "",
      name: "",
      images: ["legoBasement.jpeg"],
      rotate: false
    },
    {
      id: 13,
      title: "Pickle Boy Cover",
      desc: "",
      name: "",
      images: ["pickleBoyCropped.jpeg"],
      rotate: false
    },
    {
      id: 14,
      title: "Pidgeon",
      desc: "",
      name: "",
      images: ["pidgeonCropped.jpg"],
      rotate: false
    },
    {
      id: 15,
      title: "Mr. Peterson",
      desc: "",
      name: "",
      images: ["petersonCropped.jpg"],
      rotate: false
    },
    {
      id: 16,
      title: "Shack",
      desc: "",
      name: "",
      images: ["shack.jpg"],
      rotate: false
    },
    {
      id: 17,
      title: "Mr. Simonelli",
      desc: "",
      name: "",
      images: ["simonelliCropped.jpg"],
      rotate: false
    },
    {
      id: 18,
      title: "Tony",
      desc: "",
      name: "",
      images: ["tonyCropped.jpg"],
      rotate: false
    },
    {
      id: 19,
      title: "Tzvi",
      desc: "",
      name: "",
      images: ["tzviCroppd.jpg"],
      rotate: false
    }

  ],
  videos: [
    {
      id: 20,
      title: "Moishe's Basement",
      desc: "",
      name: "",
      images: ["ppSlideShow.mp4"]
    },


    {
      id: 21,
      title: "P2 Formula",
      desc: "",
      name: "",
      images: ["p2formula.mp4"]
    },


    {
      id: 22,
      title: "Pickle Boy Intro",
      desc: "",
      name: "",
      images: ["pickleBoyIntro.mp4"]
    },

    {
      id: 23,
      title: "Prince Caprico",
      desc: "",
      name: "",
      images: ["princeCaprico.mp4"]
    },
  ]

}

// Apply admin token verification middleware if this endpoint requires authorization
app.get("/message", verifyAdminToken, (req, res) => {
  res.json({ message: "Hello from server!" });
});

app.get("/submissions", (req, res) => {
  console.log('Submissions endpoint called');
  // Create a deep copy of submittedArtwork to avoid modifying the original
  const artwork = JSON.parse(JSON.stringify(submittedArtwork));

  // Ensure all image paths are properly formatted for listeningToPickleBoy
  if (artwork.listeningToPickleBoy) {
    artwork.listeningToPickleBoy = artwork.listeningToPickleBoy.map(item => {
      item.images = item.images.map(audioPath => {
        if (audioPath.startsWith('http') || audioPath.startsWith('images/')) {
          return audioPath;
        }
        return `images/${audioPath}`;
      });
      return item;
    });
  }

  // Ensure all image paths are properly formatted
  artwork.drawings = artwork.drawings.map(item => {
    // Format each image path in the images array
    item.images = item.images.map(imagePath => {
      // If the path already starts with 'http' or 'images/', leave it as is
      if (imagePath.startsWith('http') || imagePath.startsWith('images/')) {
        return imagePath;
      }
      // Otherwise, prepend 'images/' to the path
      return `images/${imagePath}`;
    });
    return item;
  });

  // Do the same for videos
  if (artwork.videos) {
    artwork.videos = artwork.videos.map(item => {
      item.images = item.images.map(videoPath => {
        if (videoPath.startsWith('http') || videoPath.startsWith('images/')) {
          return videoPath;
        }
        return `images/${videoPath}`;
      });
      return item;
    });
  }

  console.log('Sending submissions response with', artwork.listeningToPickleBoy.length, 'listening submissions,', artwork.drawings.length, 'drawings and', artwork.videos.length, 'videos');
  console.log('Sample listening submission path:', artwork.listeningToPickleBoy[0]?.images[0]);
  console.log('Sample drawing image path:', artwork.drawings[0]?.images[0]);
  console.log('Sample video image path:', artwork.videos[0]?.images[0]);

  res.json({ submittedArtwork: artwork });
});

app.get("/purimContest2025", verifyAdminToken, (req, res) => {
  const contestPurim2025Pictures = getContestImages();
  res.json({ contestPurim2025Pictures });
});

// Public endpoint for Purim contest images that doesn't require authentication
app.get("/publicPurimContest2025", (req, res) => {
  const contestPurim2025Pictures = getContestImages();
  res.json({ contestPurim2025Pictures });
});

// Endpoint for Official Artwork
app.get("/officialArtwork", (req, res) => {
  try {
    // Construct full URLs for the images
    // Assuming images are served statically from the 'server' directory mapped to '/images'
    const artworkWithUrls = submittedArtwork.drawings.map(item => {
      // Use the first image in the array
      const imageName = item.images[0];
      // Construct the URL. Use SERVER_BASE_URL from .env if available, otherwise default.
      // Ensure the base URL doesn't end with a slash and the image path doesn't start with one.
      const baseUrl = (process.env.SERVER_BASE_URL || 'https://pickle-boy-backend.onrender.com').replace(/\/$/, '');
      const imagePath = `/images/${imageName.split('/').pop()}`;
      return {
        ...item,
        image: `${baseUrl}${imagePath}`
      };
    });

    res.json({ officialArtwork: artworkWithUrls }); // Match the structure expected by the frontend
  } catch (error) {
    console.error("Error fetching official artwork:", error);
    res.status(500).json({ error: "Failed to fetch official artwork" });
  }
});

app.post("/contestData", async (req, res) => {
  console.log('POST /contestData endpoint hit');
  try {
    const voteObject = req.body;

    // Validate input
    if (!voteObject || typeof voteObject !== 'object') {
      return res.status(400).json({
        success: false,
        message: "Invalid vote data format"
      });
    }

    const result = await voteDB(voteObject);
    res.json({ success: true, result });
  } catch (error) {
    console.error("Error processing vote:", error);
    res.status(500).json({ success: false, message: "Error processing vote" });
  }
});

var genericSubject = "Hi there!";
var genericMessage = "Thanks for reaching out! We received your message and will get back to you as soon as possible.";
var subscribeMessage = "Thanks for reaching out! You'll be added to the mailing list as soon as possible"

async function mail(from, to, subject, text) {
  try {
    // Validate email format
    if (!validateEmail(to) || !validateEmail(from)) {
      throw new Error('Invalid email format');
    }

    const transporter = nodemailer.createTransport({
      service: 'gmail',
      secure: true, // Enforce TLS
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      tls: {
        rejectUnauthorized: true // Reject unauthorized TLS/SSL connections
      }
    });

    const mailOptions = {
      from: from,
      to: sanitizeInput(to),
      subject: sanitizeInput(subject),
      text: sanitizeInput(text),
      // Security measures for email
      disableUrlAccess: true,
      disableFileAccess: true
    };

    console.log("Sending email to:", to);
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', sanitizeLog(info.response)); // Sanitize log output
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

/* Commented out problematic code
  costumes2025: [
    {
      id: 1,
      title: "Blackjack",
      age: 10,
      name: "Ezra Rudy",
      images: ["blackjackRudy.jpeg"]
    },
    {
      id: 2,
      title: "The Voice",
      age: 15,
      name: "Yitzchok Maierovits",
      images: ["theVoiceMajorovitz.jpg"]
    },
    {
      id: 3,
      title: "PickleBoy",
      age: 1,
      name: "Chaim Fleisher",
      images: ["FleisherPB1.jpg", "FleisherPB2.jpg", "FleisherPB3.jpg"]
    },
    {
      id: 4,
      title: "BlackJack",
      age: 11,
      name: "S. Ostreicher",
      images: ["OstreicherBJ1.jpg", "OstreicherBJ2.jpg"]
    },
    {
      id: 5,
      title: "PickleBoy",
      age: 1,
      name: "unknown",
      images: ["lightPB.jpeg"]
    },
    {
      id: 6,
      title: "Miri",
      age: 1,
      name: "unknown",
      images: ["StavskyMiri.jpg"]
    },
    {
      id: 7,
      title: "Peterson",
      age: 1,
      name: "unknown",
      images: ["harrisPeterson.jpeg"]
    },
    {
      id: 8,
      title: "BlackJack",
      age: 11,
      name: "Yehuda Aryeh Goldberg",
      images: ["goldbergBlackjack1.jpeg","goldbergBlackjack2.jpeg"]
    },
    {
      id: 9,
      title: "Moishy & Miri",
      age: 1,
      name: "unknown",
      images: ["NormatovaPB1.jpg", "NormatovaPB2.jpg", "NormatovaPB3.jpg", "NormatovaPB4.jpg", "NormatovaPB5.jpg"]
    },

    {
      id: 10,
      title: "PickleBoy",
      age: 9,
      name: "Eliyahu Gindi",
      images: ["gindiPB.jpeg","gindiArtwork.jpeg"]
    },
    {
      id: 11,
      title: "Gangster",
      age: 12,
      name: "Yoel Somers",
      location: "Ramat Beit Shemesh",
      images: ["SomersGangster.jpg"]
    },
    {
      id: 12,
      title: "BlackJack",
      age: 9,
      name: "eluzur berger",
      images: ["bergerBlackJack.png"]
    },
    {
      id: 13,
      title: "PickleBoy",
      age: 10,
      name: "Binyomin Simcha Newman",
      location:"Oak Park, MI",
      images: ["newmanPickleBoy1.jpeg", "newmanPickleBoy2.jpeg"]
    }
*/

// FAQ submission endpoint - reactivated and fixed
app.post("/onSubmitFAQForm", async (req, res) => {
  try {
    const { name, email, platform, question, age, location, displayName, customFirstName, type, category } = req.body;

    // Validate required fields
    if (!name || !email || !platform || !question || !age || !location || !type) {
      return res.status(400).json({ success: false, message: "All fields are required" });
    }

    // Sanitize all input
    const sanitizedData = {
      name: sanitizeInput(name),
      email: sanitizeInput(email),
      platform: sanitizeInput(platform),
      question: sanitizeInput(question),
      age: sanitizeInput(age),
      location: sanitizeInput(location),
      displayName: displayName,
      customFirstName: customFirstName ? sanitizeInput(customFirstName) : '',
      type: sanitizeInput(type),
      category: sanitizeInput(category || 'general'),
      createdAt: new Date()
    };

    // Store FAQ submission in database using the FAQs collection (same as admin endpoints)
    let client;
    try {
      client = await connectToDb();
      const result = await client.db("PickleBoy_DB").collection("FAQs").insertOne(sanitizedData);
      console.log(`FAQ submission inserted successfully with ID: ${result.insertedId}`);
    } finally {
      if (client) {
        await client.close();
      }
    }

    // Send email notifications
    let displayNameText = 'No';
    if (sanitizedData.displayName === 'displayName') {
      displayNameText = 'Yes (full name)';
    } else if (sanitizedData.displayName === 'displayFirstName') {
      const firstNameToDisplay = sanitizedData.customFirstName || sanitizedData.name.split(' ')[0];
      displayNameText = `Yes (first name only: "${firstNameToDisplay}")`;
    }

    const body = `I have a question / comment for the pickle barrel. Name: ${sanitizedData.name}. Age: ${sanitizedData.age}. Email: ${sanitizedData.email}. Platform I listen on: ${sanitizedData.platform}. Question/comment: ${sanitizedData.question}. Display my name: ${displayNameText}. Type: ${sanitizedData.type}. Category: ${sanitizedData.category}`;

    await mail(pickleBoyEmail, emailSendTo, 'Pickle Barrel form', body);
    await mail(pickleBoyEmail, sanitizedData.email, genericSubject, genericMessage);

    res.json({ success: true });
  } catch (error) {
    console.error("FAQ form error:", error);
    res.status(500).json({ success: false, message: "Error processing FAQ submission" });
  }
});

// Add endpoint to get approved FAQ entries, protected by admin auth
app.get("/api/admin/getFAQItems", verifyAdminToken, async (req, res) => {
  let client;
  try {
    client = await connectToDb();
    // Only return FAQ items that have answers (which means they're approved for display)
    const faqs = await client.db("PickleBoy_DB").collection("FAQs")
      .find({ answer: { $exists: true, $ne: "" } })
      .project({
        question: 1,
        answer: 1,
        name: 1,
        displayName: 1,
        type: 1,
        category: 1,
        age: 1
      })
      .sort({ createdAt: -1 })
      .toArray();

    res.json(faqs);
  } catch (error) {
    console.error("Error fetching FAQ items:", error);
    // Return sample FAQ data as fallback
    const sampleFaqs = [
      {
        question: 'I was traveling to Monsey with my whole family and on the way the car was getting hectic people were getting cranky...' +
          ' All I did was turn on Pickle Boy and then magic, everybody quieted down it was amazing! Keep it up! - Yaakov Balser',
        answer: 'Thanks for sharing! It’s great to hear Pickle Boy helped make your trip more enjoyable.',
        displayName: 'displayName',
        name: 'Yaakov Balser',
        type: 'comment',
        category: 'general',
        age: ''
      },

      {
        question: ' If  Dr Picolo was able to see the guard\'s screen why did he need the bird to show him what\'s going on in the outside world?' +
          ' Couldn\'t he just look at the screen and see what was happening himself? -Dovid from Waterbury',
        answer: 'Dr. Picolo mainly used the bird to keep track of what was going on near the area where he hid his formula.' +
          '  Once in a while he would hear the general news of the world from the guards\' TV screen, but if the guards caught him looking they would usually give him a beating.' +
          '  Dr. Picolo\'s main concern was his formula, though.' +
          '  He knew that if it ended up in the wrong hands, the world could change so much that the rest of the news would not really be that important anymore.   Lucky for us, Moishe was the one who found it.',
        displayName: 'dontDisplayName',
        name: 'Dovid Goldfinger',
        type: 'faq',
        category: 'general',
        age: '17'
      },
      {
        question: 'I’m very confused is this story true or not? If for some reason it’s true then how do you know it? Didnt everyone forget? -Chavy Zabner',
        answer: 'As I always end each episode, not even the storyteller is 100% sure!  If it is indeed true but something happened to make everyone forget it,' +
          ' then maybe I also only know the story so well because it may have actually happened but I forgot that it was true (similar to Miri in Q1) or' +
          ' maybe the whole thing was created out of my very active imagination.' +
          '  There is really no way to prove this one way or the other.....at least for now!',
        displayName: 'displayName',
        name: 'Chavy Zabner',
        type: 'faq',
        category: 'general',
        age: '13'
      },
      {
        question: 'How did Moishe drink the unkosher pickle juice? -Goldie Safern',
        answer: ' The formula was Kosher as Dr. Picolo, its inventor, was a religious Jew.  Moshe did not know this and did not even consider the Kashrus issue since he assumed everything in his house was Kosher (as most people do).' +
          '  In the beginning of Season 3 IYH I will address this issue very directly and we will deal with it then.\n',
        displayName: 'DisplayName',
        name: 'Goldie Safern',
        type: 'faq',
        category: 'general',
        age: '13'
      },
      {
        question: 'How old is BlackJack? -Yosef Mann',
        answer: 'He is 16',
        displayName: 'dontDisplayName',
        name: 'Yosef mann',
        type: 'faq',
        category: 'general',
        age: '7'
      },
      {
        question: 'Did the story possibly happen to Uncle Moish because his name is Moish and obviously by his bris they did' +
          ' not name him Moish they probably named him Moishy and his sisters name is Miriam which is Miri? -Batsheva Federman ',
        answer: 'Well, I sort of hint to all this during the entire Season 1, and in Episode 11 during the bonus scene at the end, this whole mystery is cleared up considerably.' +
          '  Season 2 Episode 9\'s "Bonus scene" has some more of this.  But I leave it to the listeners to put all the pieces together!',
        displayName: 'displayName',
        name: 'Batsheva Federman',
        type: 'faq',
        category: 'general',
        age: ''
      },
      {
        question: 'Will there be a season 3? -Batsheva Federman',
        answer: 'IYH',
        displayName: 'displayName',
        name: 'Batsheva Federman',
        type: 'faq',
        category: 'general',
        age: ''

      },

      {
        question: 'In an earlier episode Moshe took off his suit without being powered up, how? -Chaim Harris',
        answer: 'The juiced-up clothing retain their shape until they are removed.  Once removed, they collapse into a ball that cannot be opened by anyone not powered up by the formula.',
        displayName: 'displayName',
        name: 'Chaim Harris',
        type: 'faq',
        category: 'general',
        age: '11'
      },
      {
        question: 'Where can I listen to The Secret Adventures of Pickle Boy?',
        answer: 'You can find the series on platforms like Mostly Music, Naki Radio, 24Six, Amazon, Spotify, Apple Music, Zing, and YouTube.',
        displayName: 'dontDisplayName',
        name: '',
        type: 'faq',
        category: 'general',
        age: ''
      },
      {
        question: 'Doctor Piccolo is Jewish why doesn\’t he daven to Hashem to save him?. -Zevi Worch',
        answer: ' Of course he does!  And maybe Moshe will be the way he gets saved - or maybe not!  Or maybe he won\'t get saved.  All at the right time - as Uncle Moish always says.',
        displayName: 'displayName',
        name: 'Zevi worch',
        type: 'faq',
        category: 'general',
        age: '13'
      },
      {
        question: '1313 East 13th is a fake address- I tried visiting it.',
        answer: 'Right now there is no house there, it is just an empty alley between two houses.  HOWEVER....at the time the story took place, things may have been different.  I cannot reveal any more about this Absolute Secret matter at this time.',
        displayName: 'dontDisplayName',
        name: '',
        type: 'faq',
        category: 'general',
        age: '13'
      },

      {
        question: 'If the pickle juice is 1oz =1 hour for pickle boy, would a sick person also have to drink it every hour, on the hour? Or would it have a different effect? -Sarah Cohen,',
        answer: ' Theoretically.  Maybe that\'s why Dr. Picolo made so much of it right away, because a person would have to keep drinking it.  And perhaps he would have made a longer-lasting version eventually, had he been able to continue his research.',
        displayName: 'displayName',
        name: 'Sarah Cohen,',
        type: 'faq',
        category: 'general',
        age: '11'
      },
      {
        question: 'When David Gold the FBI agent asked Uncle Moish if he is "up to date" my question is what is "up to date?" -Hershy weiss',
        answer: 'Absolute Secret!!!   All at the right time, to quote Uncle Moish.',
        displayName: 'displayName',
        name: 'Hershy Weiss',
        type: 'faq',
        category: 'season1',
        age: '14'
      },
      {
        question: 'How was Moishy able to get hurt by his fire face, if he was still powered up? -Eli',
        answer: 'Since the fire came from his overpowered brain, it was able to affect him as well.' +
          '  In effect, the protection provided by the formula would not work as well against damage that is caused by the formula.',
        displayName: "dontDisplayName",
        name: 'Eli',
        type: 'faq',
        category: 'season2',
        age: '13'

      },
      {
        question: ' Why does BlackJack steal the goalpost and what does that have to do with the story itself -Zalman Mann',
        answer: 'He took it because he wanted to.  He saw it and wanted it, so he figured, why can\'t I have it?' +
          '  This is what happens in the mind of someone who does not think at all about what is right, but whose only concern is what is good for HIM at that moment.' +
          '  Moshe, on the other hand, is not like that, or he would have used his formula on the Big Three.' +
          '  Moshe is constantly trying to do the right thing, something we can all, do, even without a special Formula. ',
        displayName: 'displayName',
        name: 'Zalman Mann',
        type: 'faq',
        category: 'season2',
        age: '14'
      },
      {
        question: 'Doesn’t anyone ever tell the Big Three\'s parents? Do they act like this to their siblings? Do they even have siblings? -Chavy zabner',
        answer: 'No one was brave enough to bring it up to the parents of the Big Three.  It is safe to say that they are not aware of what their sons are doing.' +
          '  It is also safe to say that the Big Three are not especially nice to their siblings.' +
          '  I don\'t really go into their family life too much (we have enough to deal with as it is).  But you can use your own imagination on that one. \n',
        displayName: 'displayName',
        name: 'Chavy zabner',
        type: 'faq',
        category: 'season2',
        age: '13'
      },
      {
        question: 'Why does BlackJack steal the goalpost and what does that have to do with the story itself -Zalman Mann',
        answer: 'He took it because he wanted to.  He saw it and wanted it, so he figured, why can\'t I have it?' +
          '  This is what happens in the mind of someone who does not think at all about what is right, but whose only concern is what is good for HIM at that moment.' +
          '  Moshe, on the other hand, is not like that, or he would have used his formula on the Big Three.' +
          '  Moshe is constantly trying to do the right thing, something we can all, do, even without a special Formula. ',
        displayName: 'displayName',
        name: 'Zalman Mann',
        type: 'faq',
        category: 'season2',
        age: '14'
      },
      {
        question: 'If only uncle Moish and that officer remember the absolute secret then how does Eli’s mother Miri remember? -Chavy Zabner',
        answer: 'Miri is under the assumption that she heard these stories from her big brother when she was growing up.' +
          '  It is as yet unclear if this was the case or perhaps she actually has a vague memory of the actual events but THINKS that they were just stories she heard,' +
          ' and Moish is just playing along with that assumption.  I am leaning towards this second explanation!',
        displayName: 'displayName',
        name: 'chavy zabner',
        type: 'faq',
        category: 'season2',
        age: '13'

      },

      {
        question: ' In season 1, by the explosion, it says that Mrs Greenberg put Miri to sleep early. Wasn\'t it Friday? Then, it says that Miri was by the seudah? -Ezriel Dov Fleisher.',
        answer: 'At the end of Season 1, the fact that it was Erev Shabbos was not really addressed, as the focus was on the immediate aftermath of the explosion.  At the beginning of Season 2 we see more detail about what went on in the Greenberg house that night.  I would say Miri was put to bed right after the seudah (which was probably over quicker than usual) instead of being able to stay up later, which she was sometimes allowed to do.',
        displayName: 'dontDisplayName',
        name: 'Yossi',
        type: 'faq',
        category: 'season2',
        age: '11'
      },
      {
        question: 'How did BlackJacks face change if he didnt think about to change it?',
        answer: 'He did have in mind that he did not want to be recognized by the police, so this caused his face to become slightly blurry.',
        displayName: 'displayName',
        name: 'Aaron Menache',
        type: 'faq',
        category: 'season2',
        age: '14'
      },
      {
        question: 'What happened about Miri\'s idea that night to try some pickle juice so she could be pickle boys friend. -Yossi',
        answer: 'Let\'s see if anything ever comes of this - it may have been a random thought as Miri drifted off to sleep, or maybe not! ',
        displayName: 'dontDisplayName',
        name: 'Yossi',
        type: 'faq',
        category: 'season2',
        age: '11'
      },
      {
        question: 'If Moshe got hurt from the fire face why doesn\’t he use it on black jack?',
        answer: 'This obvious idea may very possibly occur to Moshe as well, stay tuned!',
        displayName: 'dontDisplayName',
        name: 'Chaim tress',
        type: 'faq',
        category: 'season2',
        age: '13'
      },
      {
        question: ' In Season 2 Episode 9 Miri says that Moshe got chopped in half. How is that possible if Moshe drank pickle juice before that? -Sruli Tenenbaum',
        answer: 'Miri was definitely not supposed to talk about that.  But as to what exactly happened.....as Uncle Moish always says, "Everything at the right time".',
        displayName: 'DisplayName',
        name: 'Sruli Tenenbaum',
        type: 'faq',
        category: 'season2',
        age: '1o'
      }
    ];
    res.json(sampleFaqs);
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Admin endpoint for fetching submissions (protected by admin token)
app.get("/api/admin/submissions", verifyAdminToken, (req, res) => {
  res.json({ submittedArtwork });
});

// Endpoint for adding a new submission (protected by admin token)
app.post("/api/admin/submissions", csrfProtection, verifyAdminToken, upload.single('file'), async (req, res) => {
  let client;
  try {
    // Validate input
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    if (!req.body.title) {
      return res.status(400).json({ success: false, message: 'Title is required' });
    }

    // Generate a unique filename
    const timestamp = Date.now();
    const fileExtension = req.file.originalname.split('.').pop();
    const filename = `${req.body.title.replace(/\s+/g, '_')}_${timestamp}.${fileExtension}`;

    // Connect to database
    client = await connectToDb();

    // Save file to database
    const fileData = {
      filename: filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      data: req.file.buffer.toString('base64'),
      uploadedAt: new Date(),
      uploadedBy: req.admin.username
    };

    const fileResult = await client.db("PickleBoy_DB").collection("Files").insertOne(fileData);
    console.log(`File saved to database: ${filename}, size: ${req.file.size} bytes`);

    // Create a new submission object
    const newSubmission = {
      id: Date.now(), // Use timestamp as ID
      title: req.body.title,
      desc: req.body.desc || '',
      name: req.body.name || '',
      images: [filename],
      rotate: req.body.rotate === 'true',
      fileId: fileResult.insertedId // Reference to the file in database
    };

    // Add to the appropriate array based on type
    if (req.body.type === 'listeningToPickleBoy') {
      submittedArtwork.listeningToPickleBoy.push(newSubmission);
    } else if (req.body.type === 'drawing') {
      submittedArtwork.drawings.push(newSubmission);
    } else if (req.body.type === 'video') {
      submittedArtwork.videos.push(newSubmission);
    } else {
      return res.status(400).json({ success: false, message: 'Invalid submission type' });
    }

    // Return success response
    res.status(201).json({
      success: true,
      message: 'Submission added successfully',
      submission: newSubmission
    });
  } catch (error) {
    console.error('Error adding submission:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Endpoint for deleting a submission (protected by admin token)
app.delete("/api/admin/submissions/:id", csrfProtection, verifyAdminToken, async (req, res) => {
  let client;
  try {
    const id = parseInt(req.params.id);
    const type = req.body.type;

    if (!id || !type) {
      return res.status(400).json({ success: false, message: 'ID and type are required' });
    }

    let submissionToDelete = null;

    // Find and remove the submission from the appropriate array
    if (type === 'listeningToPickleBoy') {
      const index = submittedArtwork.listeningToPickleBoy.findIndex(item => item.id === id);
      if (index === -1) {
        return res.status(404).json({ success: false, message: 'Listening submission not found' });
      }
      submissionToDelete = submittedArtwork.listeningToPickleBoy[index];
      submittedArtwork.listeningToPickleBoy.splice(index, 1);
    } else if (type === 'drawing') {
      const index = submittedArtwork.drawings.findIndex(item => item.id === id);
      if (index === -1) {
        return res.status(404).json({ success: false, message: 'Drawing not found' });
      }
      submissionToDelete = submittedArtwork.drawings[index];
      submittedArtwork.drawings.splice(index, 1);
    } else if (type === 'video') {
      const index = submittedArtwork.videos.findIndex(item => item.id === id);
      if (index === -1) {
        return res.status(404).json({ success: false, message: 'Video not found' });
      }
      submissionToDelete = submittedArtwork.videos[index];
      submittedArtwork.videos.splice(index, 1);
    } else {
      return res.status(400).json({ success: false, message: 'Invalid submission type' });
    }

    // Delete associated files from database
    if (submissionToDelete && submissionToDelete.images) {
      try {
        client = await connectToDb();
        for (const filename of submissionToDelete.images) {
          await client.db("PickleBoy_DB").collection("Files").deleteOne({ filename: filename });
          console.log(`Deleted file from database: ${filename}`);
        }
      } catch (dbError) {
        console.error('Error deleting files from database:', dbError);
        // Continue with submission deletion even if file deletion fails
      }
    }

    // Return success response
    res.json({ success: true, message: 'Submission deleted successfully' });
  } catch (error) {
    console.error('Error deleting submission:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Apply admin token verification middleware to protect this endpoint
app.get("/purimContest2025", verifyAdminToken, (req, res) => {
  const contestPurim2025Pictures = getContestImages();
  res.json({ contestPurim2025Pictures });
});

// Public endpoint to get approved FAQ entries (no authentication required)
app.get("/getFAQItems", async (req, res) => {
  let client;
  try {
    console.log("Attempting to fetch FAQ items from database...");
    client = await connectToDb();
    console.log("Database connection successful");

    // Only return FAQ items that have answers (which means they're approved for display)
    const faqs = await client.db("PickleBoy_DB").collection("FAQs")
      .find({ answer: { $exists: true, $ne: "" } })
      .project({
        question: 1,
        answer: 1,
        name: 1,
        displayName: 1,
        customFirstName: 1,
        type: 1,
        category: 1,
        age: 1,
        location: 1
      })
      .sort({ createdAt: -1 })
      .toArray();

    console.log(`Found ${faqs.length} approved FAQ items in database`);

    // Return in the format expected by the frontend
    res.json({ FAQItems: faqs });
  } catch (error) {
    console.error("Error fetching FAQ items:", error);
    // Return empty array instead of sample data
    res.json({ FAQItems: [] });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

/* Commented out problematic code
    // Make sure episodeKey is valid
    const validEpisodeKeys = ['episodeOne', 'episodeTwo', 'episodeThree',
      'episodeFour', 'episodeFive', 'episodeSix', 'general'];
    if (action === 'add' && !validEpisodeKeys.includes(episodeKey)) {
      return res.status(400).json({
        success: false,
        message: "Invalid episode key"
      });
    }

  MongoClient.connect(uri, function (err, db) {
    if (err) throw err;
    var dbo = db.db("PickleBoy_DB");
    var myobj = req.body;
    dbo.collection("Votes").insertOne(myobj, function (err, res) {
      if (err) throw err;
      console.log("1 document inserted");
      db.close();
    });
  });



  //  console.log('vote object', req.body)
  //   const client = new MongoClient(uri)
  //   try{
  //      client.connect()
  //      const result = client.db("PickleBoy_DB").collection("Votes").insertOne(req.body)
  //      .then((r) => console.log("DB Connected", result, r))
  //   }
  //   catch(err){
  //     console.log('error with insert')
  //     console.log(err)
  //   }
  //   res.json({  obj: req.body });
});
*/

/* Commented out duplicate code
var genericSubject = "Hi there!";
var genericMessage = "Thanks for reaching out! We recieved your message and will get back to you as soon as possible."
var subscribeMessage = "Thanks for reaching out! You'll be added to the mailing list as soon as possible"

async function mail(from, to, subject, text) {
  var transporter = nodemailer.createTransport({
    service: 'yahoo',
    port: 465,
    secure: true,
    auth: {
      user: '<EMAIL>',
      pass: 'aydw jsoi xxvz fqer'
    }
  });


  const mailOptions = {
    from: from,
    to: to,
    subject: subject,
    text: text
  }
  console.log(mailOptions)
  transporter.sendMail(mailOptions, function (error, info) {
    if (error) {
      console.log(error);
    } else {
      console.log('Email sent: ' + info.response);
    }
  });
}
*/

/* Commented out duplicate route
app.post("/onLogin", (req, res) => {
  const { username, password } = req.body;
  // check if the record exists in the database.
  // return something that shows ur logged in.
})
*/

app.post("/onSignup", (req, res) => {
  const { username, password } = req.body;
  // check if the username exists.
  //if yes say username exists
  //if not then encrypt password and save in db.
  //insertSignup(req.body)
  // return something that shows ur logged in.
})

app.post("/onForgotUsername", (req, res) => {
  const { email } = req.body;
  //if email exists in users table
  //send an email to their email with thier username in it
  //if not send back a message saying that there is no user associated with this email.
})

app.post("/onForgotPassword", (req, res) => {
  const { username } = req.body;
  // check if the username exists.
  //if yes send an email with a link to reset password
  //if not send back this username doesnt exist
})

app.post("/resetPassword", (req, res) => {
  const { newPassword, userName } = req.body;
  //encrypt password and update the users record. save new password
})

app.post("/onSubmitSubscribeForm", (req, res) => {
  const { email, platform, message, name, ages, location } = req.body;
  // insertSubscribe(req.body);
  const body = `I want to subscribe to the mailing list. Name: ${name}. Age: ${ages}. Email: ${email}. Location: ${location}. Platform I listen on: ${platform}. Message: ${message}.`
  mail(pickleBoyEmail, emailSendTo, 'Subsribe form', body);
  mail(pickleBoyEmail, email, genericSubject, subscribeMessage);

  //wanna add try / catch. to return error
  res.json({ 'success': 1 })
});

// Error handling middleware (must be the last middleware)
app.use((err, req, res, next) => {
  console.error('Global error handler:', sanitizeLog(err.message || err)); // Sanitize error before logging
  res.status(err.status || 500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' ? 'Something went wrong!' : err.message
  });
});

// Helper function to sanitize log data
const sanitizeLog = (data) => {
  const stringified = JSON.stringify(data);
  // Remove characters that could be used for log injection (newline, carriage return)
  return stringified.replace(/[\n\r]/g, '_');
};

app.listen(PORT, () => {
  console.log(`Server listening on ${PORT}`);
});

// Conditionally define test/debug endpoints only if not in production
if (process.env.NODE_ENV !== 'production') {
  // Test database connection endpoint
  app.get('/api/test-db-connection', verifyAdminToken, async (req, res) => {
    console.log('Testing database connection...');
    let client;
    try {
      client = await connectToDb();

      // Test query to the SubmissionForm collection
      const testQuery = await client.db("PickleBoy_DB").collection("SubmissionForm").countDocuments();
      console.log('Database query successful, found', testQuery, 'submissions');

      res.status(200).json({
        success: true,
        message: 'Database connection successful',
        collectionCount: testQuery
      });
    } catch (err) {
      console.error('Database connection test failed:', err);
      res.status(500).json({
        success: false,
        message: 'Database connection failed: ' + err.message
      });
    } finally {
      if (client) {
        await client.close();
      }
    }
  });

  // Test endpoint for file uploads
  app.post('/api/test-upload', upload.single('testImage'), (req, res) => {
    console.log('Test upload request received');
    console.log('Request headers:', JSON.stringify(req.headers)); // Sanitize headers
    console.log('Request body:', JSON.stringify(req.body)); // Sanitize body
    console.log('File object:', req.file ? {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      bufferExists: !!req.file.buffer,
      bufferLength: req.file.buffer ? req.file.buffer.length : 0
    } : 'No file received');

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    res.status(200).json({
      success: true,
      message: 'File uploaded successfully',
      file: {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      }
    });
  });

  // Troubleshooting endpoint for file uploads without auth
  app.post('/api/debug/upload-submission', upload.single('image'), async (req, res) => {
    console.log('Debug upload-submission endpoint called');
    console.log('Request headers:', req.headers);
    console.log('Request body keys:', Object.keys(req.body));
    console.log('File received:', req.file ? 'Yes' : 'No');

    const { title, description } = req.body;

    if (!title || !description) {
      return res.status(400).json({
        success: false,
        message: 'Title and description are required'
      });
    }

    let responseData = {
      success: true,
      message: 'Debug submission received successfully',
      data: {
        title,
        description,
        receivedAt: new Date().toISOString()
      }
    };

    if (req.file) {
      responseData.file = {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        buffer: req.file.buffer ? 'Buffer exists' : 'No buffer'
      };

      try {
        // Test base64 conversion
        const imageBase64 = req.file.buffer.toString('base64');
        responseData.base64Length = imageBase64.length;
        responseData.imagePreview = `data:${req.file.mimetype};base64,${imageBase64.substring(0, 100)}...`;
      } catch (imgErr) {
        responseData.imageError = imgErr.message;
      }
    }

    res.status(200).json(responseData);
  });

  // Test endpoint for creating submissions without auth
  app.post('/api/test-create-submission', upload.single('image'), async (req, res) => {
    console.log('Test submission creation endpoint called');
    console.log('Request headers:', req.headers);
    console.log('Request body keys:', Object.keys(req.body));
    console.log('File received:', req.file ? 'Yes' : 'No');

    const { title, name, email, description } = req.body;
    console.log('Test submission request received:', {
      title,
      name,
      hasEmail: !!email,
      descriptionLength: description ? description.length : 0,
      hasFile: !!req.file
    });

    if (!title || !description) {
      return res.status(400).json({
        success: false,
        message: 'Title and description are required'
      });
    }

    let client;
    try {
      // Import DOMPurify for sanitization
      // const DOMPurify = require('dompurify');

      client = await connectToDb();
      console.log('MongoDB connection successful for test submission');

      // Create submission with metadata
      const submissionData = {
        title: sanitizeInput(title),
        name: name ? sanitizeInput(name) : '',
        email: email ? sanitizeInput(email) : '',
        description: sanitizeInput(description),
        approved: null, // Null means pending
        createdAt: new Date(),
        testSubmission: true // Mark as test submission
      };

      // Handle image if it exists
      if (req.file) {
        console.log('Processing test image:', {
          originalname: req.file.originalname,
          mimetype: req.file.mimetype,
          size: req.file.size,
          bufferLength: req.file.buffer ? req.file.buffer.length : 0
        });

        try {
          // Convert buffer to base64 for storing in MongoDB
          const imageBase64 = req.file.buffer.toString('base64');
          console.log('Test image converted to base64, length:', imageBase64.length);

          submissionData.image = {
            data: imageBase64,
            contentType: req.file.mimetype,
            originalName: req.file.originalname,
            size: req.file.size
          };

          // Create a URL for frontend display
          submissionData.imageUrl = `data:${req.file.mimetype};base64,${imageBase64}`;
          console.log('Test image successfully processed and added to submission data');
        } catch (imgErr) {
          console.error('Error processing test image:', imgErr);
          // Continue without the image rather than failing the whole submission
        }
      } else {
        console.log('No image file in test request to process');
      }

      console.log('Saving test submission to database...');
      const result = await client.db("PickleBoy_DB").collection("SubmissionForm").insertOne(submissionData);
      console.log("Test submission created:", result);

      // Return the full submission data including imageUrl to client for immediate display
      res.status(201).json({
        success: true,
        message: 'Test submission created successfully',
        id: result.insertedId,
        submission: {
          _id: result.insertedId,
          ...submissionData
        }
      });
    } catch (err) {
      console.error('Error creating test submission:', err);
      res.status(500).json({
        success: false,
        message: 'Failed to create test submission: ' + err.message
      });
    } finally {
      if (client) {
        await client.close();
      }
    }
  });

  // Approve test submission endpoint (for development only)
  app.get('/api/test-approve-submission', verifyAdminToken, async (req, res) => {
    console.log('Attempting to approve a test submission...');
    let client;

    try {
      client = await connectToDb();

      // Find the most recent submission that's not already approved
      const pendingSubmission = await client.db("PickleBoy_DB").collection("SubmissionForm")
        .findOne({
          approved: { $ne: true },
          testSubmission: { $exists: true }  // Prefer test submissions
        },
          { sort: { createdAt: -1 } }
        );

      if (!pendingSubmission) {
        // Try to find any pending submission if no test submission exists
        const anyPendingSubmission = await client.db("PickleBoy_DB").collection("SubmissionForm")
          .findOne({ approved: { $ne: true } },
            { sort: { createdAt: -1 } }
          );

        if (!anyPendingSubmission) {
          return res.status(404).json({
            success: false,
            message: 'No pending submissions found to approve'
          });
        }

        // Use this regular pending submission instead
        const result = await client.db("PickleBoy_DB").collection("SubmissionForm").updateOne(
          { _id: anyPendingSubmission._id },
          { $set: { approved: true, updatedAt: new Date() } }
        );

        return res.status(200).json({
          success: true,
          message: 'Regular submission approved successfully',
          submissionId: anyPendingSubmission._id,
          title: anyPendingSubmission.title
        });
      }

      // Approve the test submission
      const result = await client.db("PickleBoy_DB").collection("SubmissionForm").updateOne(
        { _id: pendingSubmission._id },
        { $set: { approved: true, updatedAt: new Date() } }
      );

      console.log("Test submission approval result:", result);

      res.status(200).json({
        success: true,
        message: 'Test submission approved successfully',
        submissionId: pendingSubmission._id,
        title: pendingSubmission.title
      });

    } catch (err) {
      console.error('Error approving test submission:', err);
      res.status(500).json({
        success: false,
        message: 'Failed to approve test submission: ' + err.message
      });
    } finally {
      if (client) {
        await client.close();
      }
    }
  });
} // End of non-production endpoints


// Admin API endpoints - Dependencies loaded here
const bcrypt = require('bcryptjs');
// JWT already imported at the top of the file
// Cookie parser and CSRF middleware have been moved to the top of the file

// Middleware to make CSRF token available to templates/frontend if needed
// Example: app.get('/subscribe-form', (req, res) => { res.render('subscribe', { csrfToken: req.csrfToken() }); });
// Or provide an endpoint for the frontend to fetch the token

// Create a separate, more lenient rate limiter specifically for the CSRF token endpoint
const csrfTokenLimiter = rateLimit({
  max: 10, // limit each IP to 10 requests per windowMs
  windowMs: 60 * 1000, // 1 minute
  message: 'Too many CSRF token requests, please try again in a minute',
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Apply the specific rate limiter to the CSRF token endpoint
// Note: This endpoint is NOT protected by CSRF since it's needed to get the initial token
app.get('/api/csrf-token', csrfTokenLimiter, (req, res) => {
  try {
    // Create a temporary CSRF middleware instance just for token generation
    const tempCsrf = csrf({ cookie: true });

    // Apply the middleware to generate the token
    tempCsrf(req, res, (err) => {
      if (err) {
        console.error('Error generating CSRF token:', err);
        return res.status(500).json({ error: 'Failed to generate CSRF token' });
      }

      // Set a longer cache time for the CSRF token
      res.set('Cache-Control', 'private, max-age=3600'); // Cache for 1 hour
      res.json({ csrfToken: req.csrfToken() });
    });
  } catch (error) {
    console.error('Error in CSRF token endpoint:', error);
    res.status(500).json({ error: 'Failed to generate CSRF token' });
  }
});

// JWT configuration and middleware already defined at the top of the file

// Admin login endpoint
app.post('/api/admin/login', csrfProtection, async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ success: false, message: 'Username and password are required' });
    }

    // Combine username and password for verification (same as in generate-admin-hash.js)
    const combinedValue = `${username}:${password}`;

    // Get the stored hash from environment variables
    const storedHash = process.env.ADMIN_PASSWORD_HASH;

    if (!storedHash) {
      console.error('Admin password hash not found in environment variables');
      return res.status(500).json({ success: false, message: 'Server configuration error' });
    }

    // Compare the provided credentials with the stored hash
    const isMatch = await bcrypt.compare(combinedValue, storedHash);

    if (!isMatch) {
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    // Create a JWT token
    const token = jwt.sign({ username }, JWT_SECRET, { expiresIn: '24h' });

    // Set the token as an HTTP-only cookie
    res.cookie('adminToken', token, {
      httpOnly: true,
      secure: false, // Set to false for development
      sameSite: 'Lax',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });

    console.log('Setting adminToken cookie for user:', username);

    // Send success response
    res.json({ success: true, message: 'Login successful', token });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({ success: false, message: 'An error occurred during login' });
  }
});

// Validate admin session endpoint
app.get('/api/admin/validate-session', (req, res) => {
  // Check if admin token exists without using the middleware that returns 401
  const token = req.cookies && req.cookies.adminToken;

  if (!token) {
    return res.status(401).json({ success: false, message: 'No authentication token provided' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    res.json({ success: true, message: 'Session is valid', user: decoded.username });
  } catch (err) {
    console.error(`Token verification failed: ${err.name} - ${err.message}`);
    return res.status(401).json({ success: false, message: 'Invalid or expired token' });
  }
});

// Admin logout endpoint
app.post('/api/admin/logout', csrfProtection, (req, res) => {
  res.clearCookie('adminToken');
  res.json({ success: true, message: 'Logged out successfully' });
});

// Get unanswered FAQs endpoint
app.get('/api/admin/unanswered-faqs', verifyAdminToken, async (req, res) => {
  let client;

  try {
    client = await connectToDb();

    const unansweredFaqs = await client.db("PickleBoy_DB").collection("FAQs")
      .find({ answer: { $exists: false } })
      .sort({ createdAt: -1 })
      .toArray();

    res.json({ success: true, data: unansweredFaqs });
  } catch (err) {
    console.error('Error fetching unanswered FAQs:', err);
    res.status(500).json({ success: false, message: 'Failed to fetch unanswered FAQs' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Get all FAQs endpoint
app.get('/api/admin/all-faqs', verifyAdminToken, async (req, res) => {
  let client;

  try {
    client = await connectToDb();

    const allFaqs = await client.db("PickleBoy_DB").collection("FAQs")
      .find({})
      .sort({ createdAt: -1 })
      .toArray();

    res.json({ success: true, data: allFaqs });
  } catch (err) {
    console.error('Error fetching all FAQs:', err);
    res.status(500).json({ success: false, message: 'Failed to fetch all FAQs' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Answer FAQ endpoint
app.post('/api/admin/answer-faq/:id', csrfProtection, verifyAdminToken, async (req, res) => {
  let client;

  try {
    const { id } = req.params;
    const { answer, category } = req.body;

    if (!answer) {
      return res.status(400).json({ success: false, message: 'Answer is required' });
    }

    client = await connectToDb();

    const result = await client.db("PickleBoy_DB").collection("FAQs").updateOne(
      { _id: new ObjectId(id) },
      { $set: { answer, category: category || 'general', updatedAt: new Date() } }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({ success: false, message: 'FAQ not found' });
    }

    res.json({ success: true, message: 'FAQ answered successfully' });
  } catch (err) {
    console.error('Error answering FAQ:', err);
    res.status(500).json({ success: false, message: 'Failed to answer FAQ' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Delete FAQ endpoint
app.delete('/api/admin/delete-faq/:id', csrfProtection, verifyAdminToken, async (req, res) => {
  let client;

  try {
    const { id } = req.params;

    client = await connectToDb();

    const result = await client.db("PickleBoy_DB").collection("FAQs").deleteOne(
      { _id: new ObjectId(id) }
    );

    if (result.deletedCount === 0) {
      return res.status(404).json({ success: false, message: 'FAQ not found' });
    }

    res.json({ success: true, message: 'FAQ deleted successfully' });
  } catch (err) {
    console.error('Error deleting FAQ:', err);
    res.status(500).json({ success: false, message: 'Failed to delete FAQ' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Create FAQ endpoint
app.post('/api/admin/create-faq', csrfProtection, verifyAdminToken, async (req, res) => {
  let client;

  try {
    const { question, name, age, episode, displayName, customFirstName, type } = req.body;

    if (!question) {
      return res.status(400).json({ success: false, message: 'Question is required' });
    }

    client = await connectToDb();

    // Sanitize inputs to prevent injection attacks
    const sanitizedQuestion = sanitizeInput(question);
    const sanitizedName = name ? sanitizeInput(name) : '';
    const sanitizedAge = age ? (typeof age === 'string' ? sanitizeInput(age) : age) : '';
    const sanitizedCategory = episode ? sanitizeInput(episode) : 'general';
    const sanitizedDisplayName = displayName ? sanitizeInput(displayName) : 'dontDisplayMyName';
    const sanitizedType = type ? sanitizeInput(type) : 'qweston';

    const newFaq = {
      question: sanitizedQuestion,
      name: sanitizedName,
      age: sanitizedAge,
      category: sanitizedCategory,
      displayName: sanitizedDisplayName,
      customFirstName: customFirstName ? sanitizeInput(customFirstName) : '',
      type: sanitizedType,
      createdAt: new Date()
    };

    const result = await client.db("PickleBoy_DB").collection("FAQs").insertOne(newFaq);

    res.status(201).json({
      success: true,
      message: 'FAQ created successfully',
      id: result.insertedId
    });
  } catch (err) {
    console.error('Error creating FAQ:', err);
    res.status(500).json({ success: false, message: 'Failed to create FAQ' });
  } finally {
    if (client) {
      await client.close();
    }
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});
