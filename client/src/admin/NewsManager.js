import React, { useState, useEffect, useCallback } from 'react'; // Added useCallback
import '../styles.css';

function NewsManager({ url, csrfToken, fetchCsrfToken }) { // Accept url, csrfToken, and fetchCsrfToken props
  const [newsItems, setNewsItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [newItemTitle, setNewItemTitle] = useState('');
  const [newItemContent, setNewItemContent] = useState('');
  const [editingId, setEditingId] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [editContent, setEditContent] = useState('');


  // Wrap fetchNews in useCallback
  const fetchNews = useCallback(async () => {
    try {
      setLoading(true);
      // Construct the full API URL
      const apiUrl = url.endsWith('/') ? `${url}api/news` : `${url}/api/news`;
      console.log('Fetching news from:', apiUrl);
      const response = await fetch(apiUrl); // Use apiUrl
      console.log('News fetch response status:', response.status);
      if (!response.ok) {
        const errorText = await response.text(); // Get more error details
        console.error('News fetch error:', response.status, response.statusText, errorText);
        throw new Error(`Failed to fetch news items: ${response.status} ${response.statusText} - ${errorText}`);
      }
      const data = await response.json();
      console.log('News data received:', data);
      setNewsItems(data);
      setLoading(false);
    } catch (err) {
      console.error('Error in fetchNews:', err);
      setError(err.message);
      setLoading(false);
    }
  }, [url]); // Added url dependency

  useEffect(() => {
    fetchNews();
  }, [fetchNews]); // Added fetchNews dependency

  const handleAddNews = async (e) => {
    e.preventDefault();
    if (!newItemTitle.trim() || !newItemContent.trim()) return;

    try {
      // Get CSRF token if not available
      let token = csrfToken;
      if (!token && fetchCsrfToken) {
        token = await fetchCsrfToken();
        if (!token) {
          throw new Error('Failed to get CSRF token');
        }
      }

      // Construct the full API URL
      const apiUrl = url.endsWith('/') ? `${url}api/news` : `${url}/api/news`;
      console.log('Adding news to:', apiUrl);
      console.log('News data:', { title: newItemTitle, content: newItemContent });
      console.log('Using CSRF token:', token);

      const response = await fetch(apiUrl, { // Use apiUrl
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-csrf-token': token, // Include CSRF token
        },
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          title: newItemTitle,
          content: newItemContent,
          date: new Date().toISOString(),
        }),
      });

      console.log('Add news response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Add news error:', response.status, response.statusText, errorText);
        throw new Error(`Failed to add news item: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const newItem = await response.json();
      console.log('New news item created:', newItem);
      setNewsItems([...newsItems, newItem]);
      setNewItemTitle('');
      setNewItemContent('');
    } catch (err) {
      console.error('Error in handleAddNews:', err);
      setError(err.message);
    }
  };

  const handleDeleteNews = async (id) => {
    try {
      // Get CSRF token if not available
      let token = csrfToken;
      if (!token && fetchCsrfToken) {
        token = await fetchCsrfToken();
        if (!token) {
          throw new Error('Failed to get CSRF token');
        }
      }

      // Construct the full API URL
      const apiUrl = url.endsWith('/') ? `${url}api/news/${id}` : `${url}/api/news/${id}`;
      const response = await fetch(apiUrl, { // Use apiUrl
        method: 'DELETE',
        headers: {
          'CSRF-Token': token, // Include CSRF token
        },
        credentials: 'include', // Include cookies in the request
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete news item: ${response.status} ${response.statusText} - ${errorText}`);
      }

      setNewsItems(newsItems.filter(item => item._id !== id));
    } catch (err) {
      setError(err.message);
    }
  };

  const startEditing = (item) => {
    setEditingId(item._id);
    setEditTitle(item.title);
    setEditContent(item.content);
  };

  const cancelEditing = () => {
    setEditingId(null);
    setEditTitle('');
    setEditContent('');
  };

  const handleUpdateNews = async (id) => {
    try {
      // Get CSRF token if not available
      let token = csrfToken;
      if (!token && fetchCsrfToken) {
        token = await fetchCsrfToken();
        if (!token) {
          throw new Error('Failed to get CSRF token');
        }
      }

      // Construct the full API URL
      const apiUrl = url.endsWith('/') ? `${url}api/news/${id}` : `${url}/api/news/${id}`;
      const response = await fetch(apiUrl, { // Use apiUrl
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'CSRF-Token': token, // Include CSRF token
        },
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          title: editTitle,
          content: editContent,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update news item: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const updatedItem = await response.json();
      setNewsItems(newsItems.map(item =>
        item._id === id ? updatedItem : item
      ));
      setEditingId(null);
    } catch (err) {
      setError(err.message);
    }
  };

  if (loading) return (
    <div className="loading-spinner">
      <div className="spinner"></div>
      <p>Loading news items...</p>
    </div>
  );

  if (error) return (
    <div className="error-message">
      <p>Error: {error}</p>
    </div>
  );

  return (
    <div className="faqs-section">
      <div className="section-header">
        <h2>Manage News</h2>
      </div>

      <div className="create-form">
        <h3>Add News Item</h3>
        <form onSubmit={handleAddNews}>
          <div className="form-group">
            <label>Title:</label>
            <input
              type="text"
              value={newItemTitle}
              onChange={(e) => setNewItemTitle(e.target.value)}
              placeholder="News title"
              required
            />
          </div>
          <div className="form-group">
            <label>Content:</label>
            <textarea
              value={newItemContent}
              onChange={(e) => setNewItemContent(e.target.value)}
              placeholder="News content"
              rows="4"
              required
            />
          </div>
          <button type="submit" className="admin-button save">Add News Item</button>
        </form>
      </div>

      <div className="section-header">
        <h2>Current News Items</h2>
      </div>

      {newsItems.length === 0 ? (
        <p className="empty-message">No news items found.</p>
      ) : (
        <div className="faqs-list">
          {newsItems.map((item) => (
            <div key={item._id} className="faq-item">
              {editingId === item._id ? (
                <div className="faq-answer-form">
                  <div className="form-group">
                    <label>Title:</label>
                    <input
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                    />
                  </div>
                  <div className="form-group">
                    <label>Content:</label>
                    <textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      rows="4"
                    />
                  </div>
                  <div className="faq-action-buttons">
                    <button onClick={() => handleUpdateNews(item._id)} className="admin-button save">Save</button>
                    <button onClick={cancelEditing} className="admin-button cancel">Cancel</button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="faq-details">
                    <h4 className="faq-question">{item.title}</h4>
                    <p className="faq-answer">{item.content}</p>
                    <div className="faq-info">
                      <span>Date: {new Date(item.date).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="faq-action-buttons">
                    <button onClick={() => startEditing(item)} className="admin-button">Edit</button>
                    <button onClick={() => handleDeleteNews(item._id)} className="admin-button delete">Delete</button>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default NewsManager;
