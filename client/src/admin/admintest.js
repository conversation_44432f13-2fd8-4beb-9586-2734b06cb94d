import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import '../styles.css';
import './admin.css';
import NewsManager from './NewsManager';
import SubmissionsManager from './SubmissionsManager';

// --- Helper Functions ---
const mapEpisodeToReadable = (episodeKey) => {
    switch (episodeKey) {
        case 'general': return 'General';
        case 'season1': case 'episodeOne': case 'episodeTwo': case 'episodeThree': return 'Season 1';
        case 'season2': case 'episodeFour': case 'episodeFive': case 'episodeSix': return 'Season 2';
        default: return 'General';
    }
};

// --- API Context and Provider ---
const ApiContext = createContext();

const ApiProvider = ({ url, children }) => {
    const [csrfToken, setCsrfToken] = useState('');
    const [isFetchingCsrf, setIsFetchingCsrf] = useState(false);

    const fetchCsrfToken = useCallback(async () => {
        if (csrfToken || isFetchingCsrf) return csrfToken;

        setIsFetchingCsrf(true);
        try {
            const csrfUrl = url.endsWith('/') ? `${url}api/csrf-token` : `${url}/api/csrf-token`;
            const response = await fetch(csrfUrl, { method: 'GET', credentials: 'include' });
            if (!response.ok) throw new Error('Failed to fetch CSRF token');
            const data = await response.json();
            setCsrfToken(data.csrfToken);
            return data.csrfToken;
        } catch (error) {
            console.error('CSRF token fetch error:', error);
            return null;
        } finally {
            setIsFetchingCsrf(false);
        }
    }, [url, csrfToken, isFetchingCsrf]);

    useEffect(() => {
        fetchCsrfToken();
    }, [fetchCsrfToken]);

    const request = useCallback(async (method, endpoint, body = null) => {
        const token = csrfToken || await fetchCsrfToken();
        if (!token) throw new Error('CSRF Token not available');

        const apiUrl = url.endsWith('/') ? `${url}api/${endpoint}` : `${url}/api/${endpoint}`;
        const options = {
            method,
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                'x-csrf-token': token,
            },
        };
        if (body) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(apiUrl, options);
        if (!response.ok) {
            let errorData;
            try {
                errorData = await response.json();
            } catch (e) {
                // Handle cases where the error response is not valid JSON
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            throw new Error(errorData.message || `Request failed with status ${response.status}`);
        }
        return response.json();
    }, [csrfToken, fetchCsrfToken, url]);

    const api = {
        get: async (endpoint) => request('GET', endpoint),
        post: async (endpoint, body) => request('POST', endpoint, body),
        delete: async (endpoint) => request('DELETE', endpoint),
    };

    return <ApiContext.Provider value={{ api, fetchCsrfToken, csrfToken, url }}>{children}</ApiContext.Provider>;
};

const useApi = () => useContext(ApiContext);

// --- Authentication Hook and Component ---
const useAuth = () => {
    const [loggedIn, setLoggedIn] = useState(false);
    const [loading, setLoading] = useState(true);
    const { api } = useApi();

    const validateSession = useCallback(async () => {
        setLoading(true);
        try {
            const data = await api.get('admin/validate-session');
            setLoggedIn(data.success);
        } catch (error) {
            // Expected when not logged in - don't log as error
            setLoggedIn(false);
        } finally {
            setLoading(false);
        }
    }, [api]);

    useEffect(() => {
        validateSession();
    }, [validateSession]);

    const login = async (username, password) => {
        const data = await api.post('admin/login', { username, password });
        if (data.success) {
            setLoggedIn(true);
        }
        return data;
    };

    const logout = async () => {
        try {
            await api.post('admin/logout');
        } finally {
            setLoggedIn(false);
        }
    };

    return { loggedIn, loading, login, logout };
};

const Auth = () => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const { login } = useAuth();

    const handleLogin = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');
        try {
            const data = await login(username, password);
            if (!data.success) {
                setError(data.message || 'Login failed. Please check your credentials.');
            }
            // No 'onSuccess' callback needed; the hook handles the state change.
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="admin-login-container">
            <div className="admin-login-box">
                <h2>Pickle Boy Admin</h2>
                {error && <div className="admin-login-error">{error}</div>}
                <form onSubmit={handleLogin}>
                    <div className="admin-login-field">
                        <label htmlFor="username">Username</label>
                        <input type="text" id="username" value={username} onChange={(e) => setUsername(e.target.value)} required disabled={loading} />
                    </div>
                    <div className="admin-login-field">
                        <label htmlFor="password">Password</label>
                        <input type="password" id="password" value={password} onChange={(e) => setPassword(e.target.value)} required disabled={loading} />
                    </div>
                    <button type="submit" className="admin-login-button" disabled={loading}>
                        {loading ? 'Logging In...' : 'Login'}
                    </button>
                </form>
            </div>
        </div>
    );
};

// --- FAQ Components ---
const FaqForm = ({ initialData, onSubmit, onCancel, isCreating = false }) => {
    const [faqData, setFaqData] = useState(initialData || {
        question: '', name: '', age: '', episode: 'general',
        displayName: 'dontDisplayMyName', customFirstName: '', type: 'qweston'
    });
    const [loading, setLoading] = useState(false);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFaqData(prev => ({ ...prev, [name]: value }));
    };

    const handleRadioChange = (e) => {
        setFaqData(prev => ({ ...prev, displayName: e.target.value }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        await onSubmit(faqData);
        setLoading(false);
    };

    return (
        <div className="create-form">
            <h3>{isCreating ? `Create New ${faqData.type === 'qweston' ? 'Question' : 'Comment'}` : 'Edit Item'}</h3>
            <form onSubmit={handleSubmit}>
                <div className="form-group">
                    <label htmlFor="question">Question</label>
                    <textarea id="question" name="question" value={faqData.question} onChange={handleChange} rows={3} required />
                </div>
                <div className="form-group">
                    <label htmlFor="name">Name (optional)</label>
                    <input type="text" id="name" name="name" value={faqData.name} onChange={handleChange} />
                </div>
                <div className="form-group">
                    <label htmlFor="age">Age (optional)</label>
                    <input type="text" id="age" name="age" value={faqData.age} onChange={handleChange} />
                </div>
                <div className="form-group">
                    <label htmlFor="episode">Season</label>
                    <select id="episode" name="episode" value={faqData.episode} onChange={handleChange} required>
                        <option value="general">General</option>
                        <option value="season1">Season 1</option>
                        <option value="season2">Season 2</option>
                    </select>
                </div>
                <div className="form-group">
                    <label htmlFor="type">Type</label>
                    <select id="type" name="type" value={faqData.type} onChange={handleChange} required>
                        <option value="qweston">Question</option>
                        <option value="comment">Comment</option>
                    </select>
                </div>
                <div className="form-group">
                    <label>Display Name Options</label>
                    <div style={{ marginTop: '10px' }}>
                        <div style={{ marginBottom: '10px' }}>
                            <input
                                type="radio"
                                id="adminDontDisplayName"
                                name="displayName"
                                value="dontDisplayMyName"
                                checked={faqData.displayName === 'dontDisplayMyName'}
                                onChange={handleRadioChange}
                            />
                            <label htmlFor="adminDontDisplayName" style={{ marginLeft: '8px' }}>
                                DO NOT display name
                            </label>
                        </div>
                        <div style={{ marginBottom: '10px' }}>
                            <input
                                type="radio"
                                id="adminDisplayFullName"
                                name="displayName"
                                value="displayName"
                                checked={faqData.displayName === 'displayName'}
                                onChange={handleRadioChange}
                            />
                            <label htmlFor="adminDisplayFullName" style={{ marginLeft: '8px' }}>
                                DO display full name
                            </label>
                        </div>
                        <div style={{ marginBottom: '10px' }}>
                            <input
                                type="radio"
                                id="adminDisplayFirstName"
                                name="displayName"
                                value="displayFirstName"
                                checked={faqData.displayName === 'displayFirstName'}
                                onChange={handleRadioChange}
                            />
                            <label htmlFor="adminDisplayFirstName" style={{ marginLeft: '8px' }}>
                                Display first name only (customizable)
                            </label>
                        </div>
                        {faqData.displayName === 'displayFirstName' && (
                            <div style={{ marginLeft: '20px', marginTop: '10px' }}>
                                <input
                                    type="text"
                                    name="customFirstName"
                                    placeholder="Customize display name (optional)"
                                    value={faqData.customFirstName}
                                    onChange={handleChange}
                                    style={{ width: '300px', padding: '5px' }}
                                />
                                <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                                    {faqData.customFirstName.trim() === '' && faqData.name.trim() !== '' &&
                                        `Will display: "${faqData.name.split(' ')[0]}"`
                                    }
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                <div className="faq-action-buttons">
                    <button type="submit" className="admin-button save" disabled={loading}>{loading ? 'Saving...' : 'Save'}</button>
                    {onCancel && <button type="button" className="admin-button cancel" onClick={onCancel} disabled={loading}>Cancel</button>}
                </div>
            </form>
        </div>
    );
};

const FaqItem = ({ faq, onSave, onDelete }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [answerText, setAnswerText] = useState(faq.answer || '');
    const [category, setCategory] = useState(faq.category || 'general');

    const handleSave = () => {
        onSave(faq._id, { answer: answerText, category });
        setIsEditing(false);
    };

    const handleDelete = () => {
        onDelete(faq._id);
        setIsDeleting(false);
    };

    const displayNameText = () => {
        switch (faq.displayName) {
            case 'dontDisplayMyName': return 'No';
            case 'displayName': return 'Yes (full name)';
            case 'displayFirstName': return `Yes (first name: ${faq.customFirstName || (faq.name ? faq.name.split(' ')[0] : 'N/A')})`;
            default: return 'Yes';
        }
    };

    return (
        <div className="faq-item">
            <div className="faq-details">
                <p className="faq-question">{faq.question}</p>
                {faq.answer && !isEditing && <p className="faq-answer"><strong>Answer:</strong> {faq.answer}</p>}
                <div className="faq-info">
                    {faq.name && <span>From: {faq.name}</span>}
                    {faq.age && <span>Age: {faq.age}</span>}
                    <span>Category: {mapEpisodeToReadable(faq.category || 'general')}</span>
                    {faq.createdAt && <span>Date: {new Date(faq.createdAt).toLocaleDateString()}</span>}
                    {faq.email && <span style={{ wordBreak: 'break-all' }}>Email: {faq.email}</span>}
                    {faq.platform && <span>Platform: {faq.platform}</span>}
                    {faq.type && <span>Type: {faq.type}</span>}
                    <span>Status: {faq.answer ? 'Answered' : 'Unanswered'}</span>
                    <span>Display Name: {displayNameText()}</span>
                </div>
            </div>

            {isEditing ? (
                <div className="faq-answer-form">
                    <textarea value={answerText} onChange={(e) => setAnswerText(e.target.value)} placeholder="Type your answer here..." rows={4} />
                    <div className="form-group">
                        <label>Category:</label>
                        <select value={category} onChange={(e) => setCategory(e.target.value)}>
                            <option value="general">General</option>
                            <option value="season1">Season 1</option>
                            <option value="season2">Season 2</option>
                        </select>
                    </div>
                    <div className="faq-action-buttons">
                        <button className="admin-button save" onClick={handleSave}>Save</button>
                        <button className="admin-button cancel" onClick={() => setIsEditing(false)}>Cancel</button>
                    </div>
                </div>
            ) : (
                <div className="faq-action-buttons">
                    {isDeleting ? (
                        <>
                            <span>Are you sure?</span>
                            <button className="admin-button confirm-delete" onClick={handleDelete}>Confirm Delete</button>
                            <button className="admin-button cancel" onClick={() => setIsDeleting(false)}>Cancel</button>
                        </>
                    ) : (
                        <>
                            <button className="admin-button" onClick={() => setIsEditing(true)}>{faq.answer ? 'Edit' : 'Answer'} / Categorize</button>
                            <button className="admin-button delete" onClick={() => setIsDeleting(true)}>Delete</button>
                        </>
                    )}
                </div>
            )}
        </div>
    );
};

const FaqList = ({ faqs, onSave, onDelete }) => (
    <div className="faqs-list">
        {faqs.map((faq) => <FaqItem key={faq._id} faq={faq} onSave={onSave} onDelete={onDelete} />)}
    </div>
);

const FaqManager = () => {
    const [faqs, setFaqs] = useState([]);
    const [view, setView] = useState('unanswered');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [actionError, setActionError] = useState('');
    const [showCreateForm, setShowCreateForm] = useState(false);
    const { api } = useApi();

    useEffect(() => {
        if (actionError) {
            const timer = setTimeout(() => setActionError(''), 5000);
            return () => clearTimeout(timer);
        }
    }, [actionError]);

    const fetchData = useCallback(async () => {
        setLoading(true);
        setError(null);
        const endpoint = view === 'all' ? 'admin/all-faqs' : 'admin/unanswered-faqs';
        try {
            const data = await api.get(endpoint);
            setFaqs(data.data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [api, view]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleAction = async (action, successCallback) => {
        setActionError('');
        try {
            await action();
            if (successCallback) successCallback();
            fetchData();
        } catch (err) {
            setActionError(err.message);
        }
    };

    const handleSave = (id, data) => handleAction(() => api.post(`admin/answer-faq/${id}`, data));
    const handleDelete = (id) => handleAction(() => api.delete(`admin/delete-faq/${id}`));
    const handleCreate = (faqData) => handleAction(() => api.post('admin/create-faq', faqData), () => setShowCreateForm(false));

    return (
        <div className="faqs-section">
            <div className="section-header">
                <h2>{view === 'all' ? 'All FAQs' : 'Unanswered Questions'}</h2>
                <div className="section-header-buttons">
                    <button className="admin-button" onClick={() => setView(view === 'all' ? 'unanswered' : 'all')}>
                        View {view === 'all' ? 'Unanswered' : 'All'} FAQs
                    </button>
                    <button className="admin-button create-button" onClick={() => setShowCreateForm(s => !s)}>
                        {showCreateForm ? 'Cancel' : 'Create New Item'}
                    </button>
                </div>
            </div>

            {error && <div className="error-message">{error} <button onClick={fetchData}>Retry</button></div>}
            {actionError && <div className="error-message action-error">{actionError}</div>}
            {loading && <div className="loading-spinner"><div className="spinner"></div><p>Loading...</p></div>}

            {showCreateForm && <FaqForm onSubmit={handleCreate} onCancel={() => setShowCreateForm(false)} isCreating={true} />}

            {!loading && !error && (
                faqs.length > 0 ? (
                    <FaqList faqs={faqs} onSave={handleSave} onDelete={handleDelete} />
                ) : (
                    <p className="empty-message">No {view === 'all' ? '' : 'unanswered'} questions found.</p>
                )
            )}
        </div>
    );
};

// --- Main Admin Component ---
const AdminPanel = ({ onLogout }) => {
    const [activeTab, setActiveTab] = useState('faqs');
    const { url, csrfToken, fetchCsrfToken } = useApi();

    return (
        <div className="admin-container">
            <div className="admin-hero">
                <div className="admin-hero-content">
                    <div className="admin-header">
                        <h1 className="admin-title">Pickle Boy Admin Panel</h1>
                        <div className="admin-status">
                            <span className="login-status">Logged in as admin</span>
                            <button className="logout-button" onClick={onLogout}>Logout</button>
                        </div>
                    </div>
                </div>
            </div>

            <div className="admin-tabs">
                <button className={`tab-button ${activeTab === 'faqs' ? 'active' : ''}`} onClick={() => setActiveTab('faqs')}>FAQ Management</button>
                <button className={`tab-button ${activeTab === 'news' ? 'active' : ''}`} onClick={() => setActiveTab('news')}>News Management</button>
                <button className={`tab-button ${activeTab === 'submissions' ? 'active' : ''}`} onClick={() => setActiveTab('submissions')}>Submissions</button>
            </div>

            <div className="admin-content-container">
                {activeTab === 'faqs' && <FaqManager />}
                {activeTab === 'news' && <NewsManager url={url} csrfToken={csrfToken} fetchCsrfToken={fetchCsrfToken} />}
                {activeTab === 'submissions' && <SubmissionsManager url={url} csrfToken={csrfToken} fetchCsrfToken={fetchCsrfToken} />}
            </div>
        </div>
    );
};

const AdminApp = () => {
    const { loggedIn, loading, logout } = useAuth();
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        // Avoids hydration mismatches by ensuring auth logic runs client-side
        setIsClient(true);
    }, []);

    if (loading || !isClient) {
        return <div className="loading-spinner"><div className="spinner"></div></div>;
    }

    if (!loggedIn) {
        return <Auth />;
    }

    return <AdminPanel onLogout={logout} />;
};


const Admin = ({ url }) => {
    return (
        <ApiProvider url={url}>
            <AdminApp />
        </ApiProvider>
    );
};


export default Admin;